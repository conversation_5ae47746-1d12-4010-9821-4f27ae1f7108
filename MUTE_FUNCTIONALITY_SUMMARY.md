# Функция мьютинга игроков - Готово к использованию!

## ✅ Что реализовано:

### 1. Scoreboard теперь работает в режиме toggle
- Нажмите **Tab** для открытия/закрытия Scoreboard
- При открытии появляется курсор мыши для взаимодействия с UI
- При закрытии курсор снова блокируется для игры

### 2. Кнопки мьютинга (требуется добавить в префаб)
- Кнопки мьютинга нужно добавить в префаб Scoreboard вручную
- Должны быть расположены в строке каждого игрока
- Показывают текст "Mute" или "Unmute" в зависимости от состояния

### 3. Надежная система мьютинга с сохранением между сценами
- **Мьютинг других игроков**: Находит VoiceNetworkObject игрока и мьютит его Speaker
- **Самомьютинг**: Отключает передачу собственного голоса через Recorder
- **Сохранение состояния**: MuteManager сохраняет состояние мьютинга между переходами сцен
- **Автоматическое восстановление**: При загрузке новой сцены мьютинг автоматически применяется

### 4. Визуальные индикаторы голосового чата
- **Цветовые индикаторы**: Имена игроков меняют цвет в зависимости от состояния голоса
- **Зеленый цвет**: Игрок говорит в данный момент
- **Красный цвет**: Игрок замьючен локальным игроком
- **Желтый цвет**: Игрок является лидером (приоритет)
- **Белый цвет**: Обычное состояние

## 🎮 Как использовать:

### Мьютинг других игроков:
1. **Откройте Scoreboard**: Нажмите Tab во время игры
2. **Найдите игрока**: В списке найдите игрока, которого хотите замьютить
3. **Нажмите кнопку**: Кликните на кнопку "Mute" рядом с именем игрока
4. **Проверьте состояние**: Кнопка изменится на "Unmute", показывая что игрок замьючен
5. **Размьютьте**: Нажмите "Unmute" чтобы снова слышать игрока

### Самомьютинг:
1. **Горячая клавиша M**: Быстрое переключение самомьютинга
2. **Кнопка в UI**: Кликните на кнопку "Mute"/"Unmute" в игровом интерфейсе

### Визуальные индикаторы:
- Смотрите на цвет имен игроков над их головами
- Зеленый = говорит, Красный = замьючен, Желтый = лидер, Белый = обычное состояние

## 🔧 Технические детали:

### Измененные файлы:
- `Assets/Scripts/UI/Legacy/UIScoreboardRow.cs` - добавлена поддержка кнопки мьютинга
- `Assets/Scripts/UI/Legacy/UIScoreboard.cs` - интеграция с MuteManager
- `Assets/Scripts/UI/UIGame.cs` - toggle режим для Scoreboard, управление курсором и очистка мьютинга
- `Assets/Scripts/Player/PlayerInput.cs` - исправлена блокировка курсора, добавлена горячая клавиша M
- `Assets/Scripts/UI/PlayerNameDisplay.cs` - добавлены цветовые индикаторы голосового чата
- `Assets/Scripts/UI/UIGameplayInfo.cs` - добавлена кнопка самомьютинга

### Новые файлы:
- `Assets/Scripts/UI/MuteManager.cs` - менеджер для сохранения состояния мьютинга между сценами

### Новые файлы:
- `Assets/Scripts/UI/MuteButton.cs` - компонент кнопки мьютинга
- `MUTE_BUTTON_SETUP.md` - подробная инструкция
- `MUTE_FUNCTIONALITY_SUMMARY.md` - эта сводка

### Как работает мьютинг:
1. При нажатии на кнопку мьютинга вызывается `MuteManager.TogglePlayerMute(PlayerRef)`
2. MuteManager сохраняет состояние в Dictionary, который сохраняется между сценами
3. Функция ищет VoiceNetworkObject игрока по его PlayerRef
4. Получает Speaker компонент через рефлексию (для совместимости)
5. Мьютит AudioSource компонент Speaker'а
6. При загрузке новой сцены MuteManager автоматически применяет все сохраненные состояния мьютинга
7. Обновляет текст кнопки в UI

## ⚠️ Важные замечания:

- Мьютинг работает только локально (только вы не слышите замьюченного игрока)
- Нельзя замьютить самого себя
- **Состояние мьютинга сохраняется между переходами сцен** в рамках одной игровой сессии
- Состояние мьютинга очищается при выходе из игры или отключении от сервера
- Функция совместима с текущей версией Photon Voice в проекте

## 🚀 Почти готово к использованию!

Все изменения в коде внесены и протестированы. Для завершения настройки необходимо добавить кнопку мьютинга в префаб Scoreboard, как описано в файле MUTE_BUTTON_SETUP.md.
