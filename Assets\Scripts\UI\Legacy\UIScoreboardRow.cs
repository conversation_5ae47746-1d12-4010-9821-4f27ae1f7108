using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Fusion;

namespace SimpleFPS {
    public class UIScoreboardRow : MonoBehaviour {
        public CanvasGroup CanvasGroup;
        public TextMeshProUGUI Position;
        public TextMeshProUGUI Name;
        public TextMeshProUGUI Kills;
        public TextMeshProUGUI Deaths;
        public GameObject LocalPlayerGroup;
        public GameObject DeadGroup;
        public Button MuteButton;

        [HideInInspector]
        public PlayerRef PlayerRef;

        private bool isMuted = false;

        public void OnMuteButtonClicked() {
            if (UIScoreboard.Instance != null) {
                UIScoreboard.Instance.TogglePlayerMute(PlayerRef);
            }
        }

        public void UpdateMuteButtonState(bool muted) {
            isMuted = muted;
            if (MuteButton != null) {
                var buttonText = MuteButton.GetComponentInChildren<TextMeshProUGUI>();
                if (buttonText != null) {
                    buttonText.text = muted ? "Unmute" : "Mute";
                }
            }
        }
    }
}
