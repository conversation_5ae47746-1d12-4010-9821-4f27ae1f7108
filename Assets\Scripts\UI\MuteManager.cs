using System.Collections.Generic;
using UnityEngine;
using Fusion;

namespace SimpleFPS {
    /// <summary>
    /// Manages player mute states across scene transitions
    /// </summary>
    public class MuteManager : MonoBehaviour {
        private static MuteManager _instance;
        public static MuteManager Instance {
            get {
                if (_instance == null) {
                    var go = new GameObject("MuteManager");
                    _instance = go.AddComponent<MuteManager>();
                    DontDestroyOnLoad(go);
                }
                return _instance;
            }
        }

        private Dictionary<PlayerRef, bool> _mutedPlayers = new Dictionary<PlayerRef, bool>();
        private bool _isSelfMuted = false;

        private void Awake() {
            if (_instance == null) {
                _instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else if (_instance != this) {
                Destroy(gameObject);
            }
        }

        public bool IsPlayerMuted(PlayerRef playerRef) {
            return _mutedPlayers.ContainsKey(playerRef) && _mutedPlayers[playerRef];
        }

        public void SetPlayerMuted(PlayerRef playerRef, bool muted) {
            _mutedPlayers[playerRef] = muted;

            // Apply mute state immediately
            ApplyMuteToPlayer(playerRef, muted);
        }

        public void TogglePlayerMute(PlayerRef playerRef) {
            bool currentlyMuted = IsPlayerMuted(playerRef);
            SetPlayerMuted(playerRef, !currentlyMuted);
        }

        public bool IsSelfMuted() {
            return _isSelfMuted;
        }

        public void SetSelfMuted(bool muted) {
            _isSelfMuted = muted;
            ApplySelfMute(muted);
        }

        public void ToggleSelfMute() {
            SetSelfMuted(!_isSelfMuted);
        }

        private void ApplySelfMute(bool muted) {
            // Find local player's VoiceNetworkObject and mute/unmute the Recorder
            var playerObjects = FindObjectsOfType<MonoBehaviour>();
            foreach (var obj in playerObjects) {
                if (obj.GetType().Name == "VoiceNetworkObject") {
                    var networkBehaviour = obj as NetworkBehaviour;
                    if (networkBehaviour != null && networkBehaviour.Object != null &&
                        networkBehaviour.Object.HasInputAuthority) {

                        // Use reflection to get RecorderInUse property
                        var recorderProperty = obj.GetType().GetProperty("RecorderInUse");
                        if (recorderProperty != null) {
                            var recorder = recorderProperty.GetValue(obj);
                            if (recorder != null) {
                                // Use reflection to set TransmitEnabled property
                                var transmitProperty = recorder.GetType().GetProperty("TransmitEnabled");
                                if (transmitProperty != null) {
                                    transmitProperty.SetValue(recorder, !muted);
                                }
                            }
                        }
                        break;
                    }
                }
            }
        }

        /// <summary>
        /// Apply mute state to all currently spawned players
        /// Call this when scene loads to restore mute states
        /// </summary>
        public void ApplyAllMuteStates() {
            foreach (var kvp in _mutedPlayers) {
                if (kvp.Value) { // Only apply if player is muted
                    ApplyMuteToPlayer(kvp.Key, true);
                }
            }
        }

        private void ApplyMuteToPlayer(PlayerRef playerRef, bool muteState) {
            // Method 1: Try to find VoiceNetworkObject and mute its Speaker
            bool foundAndMuted = TryMuteViaVoiceNetworkObject(playerRef, muteState);

            // Method 2: Fallback - find all AudioSources on player objects and mute them
            if (!foundAndMuted) {
                TryMuteViaPlayerAudioSources(playerRef, muteState);
            }
        }

        private bool TryMuteViaVoiceNetworkObject(PlayerRef playerRef, bool muteState) {
            var playerObjects = FindObjectsOfType<MonoBehaviour>();
            foreach (var obj in playerObjects) {
                // Check if this is a VoiceNetworkObject by checking its type name
                if (obj.GetType().Name == "VoiceNetworkObject") {
                    var networkBehaviour = obj as NetworkBehaviour;
                    if (networkBehaviour != null && networkBehaviour.Object != null &&
                        networkBehaviour.Object.InputAuthority == playerRef) {

                        // Use reflection to get SpeakerInUse property
                        var speakerProperty = obj.GetType().GetProperty("SpeakerInUse");
                        if (speakerProperty != null) {
                            var speaker = speakerProperty.GetValue(obj);
                            if (speaker != null) {
                                var audioSource = ((MonoBehaviour)speaker).GetComponent<AudioSource>();
                                if (audioSource != null) {
                                    audioSource.mute = muteState;
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
            return false;
        }

        private void TryMuteViaPlayerAudioSources(PlayerRef playerRef, bool muteState) {
            // Find all NetworkBehaviours that belong to this player
            var networkObjects = FindObjectsOfType<NetworkBehaviour>();
            foreach (var netObj in networkObjects) {
                if (netObj.Object != null && netObj.Object.InputAuthority == playerRef) {
                    // Find all AudioSources on this player's objects
                    var audioSources = netObj.GetComponentsInChildren<AudioSource>();
                    foreach (var audioSource in audioSources) {
                        // Only mute AudioSources that are likely voice-related
                        if (IsVoiceAudioSource(audioSource)) {
                            audioSource.mute = muteState;
                        }
                    }
                }
            }
        }

        private bool IsVoiceAudioSource(AudioSource audioSource) {
            // Check if this AudioSource is likely used for voice chat
            var parent = audioSource.transform.parent;
            if (parent != null) {
                // Check if parent has VoiceNetworkObject or Speaker component
                if (parent.GetComponent<MonoBehaviour>()?.GetType().Name == "VoiceNetworkObject" ||
                    parent.GetComponent<MonoBehaviour>()?.GetType().Name == "Speaker") {
                    return true;
                }
            }

            // Check if the AudioSource itself has a Speaker component
            if (audioSource.GetComponent<MonoBehaviour>()?.GetType().Name == "Speaker") {
                return true;
            }

            return false;
        }

        /// <summary>
        /// Clear all mute states (useful when leaving game)
        /// </summary>
        public void ClearAllMuteStates() {
            _mutedPlayers.Clear();
            _isSelfMuted = false;
        }
    }
}
