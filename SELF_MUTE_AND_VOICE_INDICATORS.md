# Самомьютинг и визуальные индикаторы голосового чата

## ✅ Что реализовано:

### 1. Функция самомьютинга
Игрок может замьютить себя для остальных игроков (отключить передачу своего голоса).

**Способы активации:**
- **Горячая клавиша M** - переключает самомьютинг
- **Кнопка в UI** - кнопка "Mute"/"Unmute" в игровом интерфейсе

**Как работает:**
- При самомьютинге отключается `TransmitEnabled` в Recorder компоненте
- Состояние сохраняется в MuteManager между сценами
- Автоматически очищается при выходе из игры

### 2. Визуальные индикаторы состояния голосового чата
Имена игроков над головами меняют цвет в зависимости от состояния голосового чата:

**Цветовая схема:**
- 🟢 **Зеленый** - игрок говорит в данный момент
- 🔴 **Красный** - игрок замьючен локальным игроком
- 🟡 **Желтый** - игрок является лидером (приоритет над другими цветами)
- ⚪ **Белый** - обычное состояние (не говорит, не замьючен)

**Приоритет цветов:**
1. Лидер (желтый) - высший приоритет
2. Замьючен (красный)
3. Говорит (зеленый)
4. Обычное состояние (белый)

## 🎮 Как использовать:

### Самомьютинг:
1. **Клавиша M** - быстрое переключение самомьютинга
2. **Кнопка в UI** - кликните на кнопку "Mute" в игровом интерфейсе
3. **Проверка состояния** - кнопка показывает "Mute" или "Unmute"

### Визуальные индикаторы:
- Просто смотрите на имена игроков над их головами
- Цвет автоматически меняется в зависимости от состояния
- Работает в реальном времени

## 🔧 Технические детали:

### Измененные файлы:
- `Assets/Scripts/UI/MuteManager.cs` - добавлены методы самомьютинга
- `Assets/Scripts/UI/PlayerNameDisplay.cs` - добавлена цветовая индикация голосового чата
- `Assets/Scripts/UI/UIGameplayInfo.cs` - добавлена кнопка самомьютинга
- `Assets/Scripts/Player/PlayerInput.cs` - добавлена горячая клавиша M

### Новые методы в MuteManager:
```csharp
public bool IsSelfMuted()                    // Проверка состояния самомьютинга
public void SetSelfMuted(bool muted)         // Установка состояния самомьютинга
public void ToggleSelfMute()                 // Переключение самомьютинга
```

### Новые поля в PlayerNameDisplay:
```csharp
[SerializeField] private Color speakingColor = Color.green;  // Цвет для говорящих
[SerializeField] private Color mutedColor = Color.red;       // Цвет для замьюченных
```

### Новые поля в UIGameplayInfo:
```csharp
[Header("Voice Chat")]
public Button SelfMuteButton;           // Кнопка самомьютинга
public TextMeshProUGUI SelfMuteButtonText;  // Текст кнопки
```

## ⚠️ Важные замечания:

- **Самомьютинг** отключает передачу голоса для всех остальных игроков
- **Визуальные индикаторы** работают только для имен над головами игроков
- **Состояние самомьютинга** сохраняется между сценами
- **Горячая клавиша M** работает только во время игры
- **Кнопка UI** должна быть добавлена в префаб игрового интерфейса

## 🎯 Настройка в Unity Editor:

### Для кнопки самомьютинга:
1. Откройте префаб игрового UI
2. Найдите компонент UIGameplayInfo
3. Добавьте кнопку в сцену
4. Привяжите кнопку к полю `SelfMuteButton`
5. Привяжите текст кнопки к полю `SelfMuteButtonText`

### Для цветов имен:
1. Откройте префаб PlayerNameDisplay
2. Настройте цвета в полях:
   - `Speaking Color` (зеленый)
   - `Muted Color` (красный)
   - `Default Name Color` (белый)
   - `Leader Name Color` (желтый)

## ✅ Готово к использованию!

Все функции реализованы и готовы к использованию. Требуется только добавление кнопки самомьютинга в UI префаб.
