using UnityEngine;
using TMPro;
using SimpleFPS;
using Fusion;

namespace SimpleFPS {
    /// <summary>
    /// Simple player name display above head using TextMeshPro
    /// </summary>
    public class PlayerNameDisplay : MonoBehaviour {
        [Header("Name Display")]
        [SerializeField] private TextMeshPro nameText;

        [Header("Settings")]
        [SerializeField] private Color defaultNameColor = Color.white;
        [SerializeField] private Color leaderNameColor = Color.yellow;
        [SerializeField] private Color speakingColor = Color.green;
        [SerializeField] private Color mutedColor = Color.red;
        [SerializeField] private float showDistance = 10f; // Расстояние показа имени

        private PlayerController playerController;
        private string lastDisplayedName = "";
        private Camera localPlayerCamera;
        private PlayerRef playerRef;
        private Color lastNameColor;

        private void Start() {
            playerController = GetComponent<PlayerController>();

            // Get PlayerRef for this player
            if (playerController != null && playerController.Object != null) {
                playerRef = playerController.Object.InputAuthority;
            }

            // Находим камеру локального игрока
            if (PlayerController.Local != null) {
                localPlayerCamera = PlayerController.Local.GetComponentInChildren<Camera>();
            }

            // Изначально скрываем текст
            if (nameText != null) {
                nameText.gameObject.SetActive(false);
            }
        }

        private void Update() {
            if (nameText == null || playerController == null) {
                return;
            }

            // Не показываем имя для локального игрока
            if (playerController.HasInputAuthority) {
                nameText.gameObject.SetActive(false);
                return;
            }

            // Проверяем расстояние до локального игрока
            bool shouldShow = ShouldShowName();
            nameText.gameObject.SetActive(shouldShow);

            if (shouldShow) {
                UpdateNameDisplay();
                UpdateNameRotation();
                UpdateVoiceStatus();
            }
        }

        private bool ShouldShowName() {
            if (PlayerController.Local == null) return false;

            float distance = Vector3.Distance(transform.position, PlayerController.Local.transform.position);
            return distance <= showDistance;
        }

        private void UpdateNameDisplay() {
            if (playerController == null) return;

            string playerName = GetPlayerName();

            if (lastDisplayedName != playerName) {
                lastDisplayedName = playerName;
                nameText.text = playerName;
            }
        }

        private void UpdateVoiceStatus() {
            if (playerController == null || nameText == null) return;

            Color targetColor = GetVoiceStatusColor();

            if (lastNameColor != targetColor) {
                lastNameColor = targetColor;
                nameText.color = targetColor;
            }
        }

        private Color GetVoiceStatusColor() {
            // Priority: Leader > Muted > Speaking > Default
            if (playerController.IsLeader) {
                return leaderNameColor;
            }

            // Check if player is muted by local player
            if (MuteManager.Instance != null && MuteManager.Instance.IsPlayerMuted(playerRef)) {
                return mutedColor;
            }

            // Check if player is speaking
            if (IsPlayerSpeaking()) {
                return speakingColor;
            }

            return defaultNameColor;
        }

        private bool IsPlayerSpeaking() {
            // Find VoiceNetworkObject for this player and check if speaking
            var playerObjects = FindObjectsOfType<MonoBehaviour>();
            foreach (var obj in playerObjects) {
                if (obj.GetType().Name == "VoiceNetworkObject") {
                    var networkBehaviour = obj as NetworkBehaviour;
                    if (networkBehaviour != null && networkBehaviour.Object != null &&
                        networkBehaviour.Object.InputAuthority == playerRef) {

                        // Use reflection to get IsSpeaking property
                        var speakingProperty = obj.GetType().GetProperty("IsSpeaking");
                        if (speakingProperty != null) {
                            var isSpeaking = speakingProperty.GetValue(obj);
                            if (isSpeaking is bool speaking) {
                                return speaking;
                            }
                        }
                        break;
                    }
                }
            }
            return false;
        }

        private void UpdateNameRotation() {
            if (localPlayerCamera != null) {
                // Поворачиваем текст к камере игрока
                nameText.transform.LookAt(localPlayerCamera.transform);
                nameText.transform.Rotate(0, 180, 0); // Разворачиваем, чтобы текст не был зеркальным
            }
        }



        private string GetPlayerName() {
            if (GameManager.Instance != null && playerController != null) {
                // Find player data by matching PlayerController
                foreach (var kvp in GameManager.Instance.PlayerData) {
                    var playerRef = kvp.Key;
                    var playerData = kvp.Value;

                    // Check if this is our player by comparing with network runner
                    if (playerController.Object != null && playerController.Object.InputAuthority == playerRef) {
                        return string.IsNullOrEmpty(playerData.Nickname) ? "Player" : playerData.Nickname;
                    }
                }
            }
            return "Player";
        }
    }
}
