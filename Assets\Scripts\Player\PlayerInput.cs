using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.EventSystems;
using Fusion;
using Fusion.Addons.SimpleKCC;
using SimpleFPS;


public enum EInputButton {
    Jump,
    Fire,
    Reload,
    Pistol,
    Rifle,
    Shotgun,
    Spray,
    Use,
    Drop,
    ItemAbility,
    Sprint,
    ClimbHold // Новая кнопка для удержания зацепки
}

/// <summary>
/// Input structure sent over network to the server.
/// </summary>
public struct NetworkedInput : INetworkInput {
    public Vector2 MoveDirection;
    public Vector2 LookRotationDelta;
    public NetworkButtons Buttons;
}

/// <summary>
/// Handles player input.
/// </summary>
[DefaultExecutionOrder(-10)]
public sealed class PlayerInput : NetworkBehaviour, IBeforeUpdate {
    public static float LookSensitivity;

    private NetworkedInput _accumulatedInput;
    private Vector2Accumulator _lookRotationAccumulator = new Vector2Accumulator(0.02f, true);
    private bool _userUnlockedCursor = false; // Track if user intentionally unlocked cursor

    public static PlayerInput Instance { get; private set; }

    public override void Spawned() {
        if (HasInputAuthority == false)
            return;

        Instance = this;

        // Register to Fusion input poll callback.
        var networkEvents = Runner.GetComponent<NetworkEvents>();
        if (networkEvents != null) {
            networkEvents.OnInput.AddListener(OnInput);
        }
        else {
            Debug.LogError("[PlayerInput] NetworkEvents component not found on Runner!");
        }

        // Force cursor lock multiple times to ensure it works
        for (int i = 0; i < 3; i++) {
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
        }

        // Also schedule a delayed lock in case it doesn't work immediately
        Invoke(nameof(ForceCursorLock), 0.1f);
    }

    public override void Despawned(NetworkRunner runner, bool hasState) {
        if (runner == null)
            return;

        var networkEvents = runner.GetComponent<NetworkEvents>();
        if (networkEvents != null) {
            networkEvents.OnInput.RemoveListener(OnInput);
        }
    }

    private void ForceCursorLock() {
        if (HasInputAuthority) {
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
        }
    }

    public void SetCursorUnlocked(bool unlocked) {
        _userUnlockedCursor = unlocked;
    }

    void IBeforeUpdate.BeforeUpdate() {
        // This method is called BEFORE ANY FixedUpdateNetwork() and is used to accumulate input from Keyboard/Mouse.
        // Input accumulation is mandatory - this method is called multiple times before new forward FixedUpdateNetwork() - common if rendering speed is faster than Fusion simulation.

        if (HasInputAuthority == false)
            return;

        // ESC key is used for unlocking cursor (for UI/menus), any mouse click re-locks it
        var keyboard = Keyboard.current;
        var mouse = Mouse.current;

        if (keyboard != null && keyboard.escapeKey.wasPressedThisFrame) {
            // ESC unlocks cursor for UI interaction
            _userUnlockedCursor = true;
            Cursor.lockState = CursorLockMode.None;
            Cursor.visible = true;
        }
        else if (keyboard != null && keyboard.tabKey.wasPressedThisFrame) {
            // Tab key toggles scoreboard - let UIGame handle cursor state
            // Don't interfere with cursor when Tab is pressed
            return;
        }
        else if (mouse != null && (mouse.leftButton.wasPressedThisFrame || mouse.rightButton.wasPressedThisFrame)) {
            // Only re-lock cursor if NOT clicking on UI elements
            if (!EventSystem.current.IsPointerOverGameObject()) {
                // Mouse click in game world re-locks cursor for gameplay
                _userUnlockedCursor = false;
                Cursor.lockState = CursorLockMode.Locked;
                Cursor.visible = false;
            }
        }

        // AGGRESSIVE CURSOR LOCK: Only auto-lock if user didn't intentionally unlock it
        // Also check if Scoreboard is open
        bool isScoreboardOpen = UIGame.Instance != null && UIGame.Instance.ScoreboardView.activeSelf;
        if (!_userUnlockedCursor && !isScoreboardOpen && Cursor.lockState != CursorLockMode.Locked) {
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
        }

        // Accumulate input only if the cursor is locked.
        if (Cursor.lockState != CursorLockMode.Locked) {
            return;
        }

        // Use the mouse variable already declared above
        if (mouse != null) {
            var mouseDelta = mouse.delta.ReadValue();

            var lookRotationDelta = new Vector2(-mouseDelta.y, mouseDelta.x);
            lookRotationDelta *= LookSensitivity / 60f;
            _lookRotationAccumulator.Accumulate(lookRotationDelta);


            _accumulatedInput.Buttons.Set(EInputButton.Use, keyboard.eKey.isPressed);
            _accumulatedInput.Buttons.Set(EInputButton.ClimbHold, keyboard.eKey.isPressed); // Удержание E для скалолазания
            _accumulatedInput.Buttons.Set(EInputButton.ItemAbility, mouse.leftButton.isPressed);
        }

        if (keyboard != null) {
            var moveDirection = Vector2.zero;

            if (keyboard.wKey.isPressed) { moveDirection += Vector2.up; }
            if (keyboard.sKey.isPressed) { moveDirection += Vector2.down; }
            if (keyboard.aKey.isPressed) { moveDirection += Vector2.left; }
            if (keyboard.dKey.isPressed) { moveDirection += Vector2.right; }


            //_accumulatedInput.Buttons.Set(EInputButton.Drop, keyboard.qKey.isPressed);

            _accumulatedInput.MoveDirection = moveDirection.normalized;

            _accumulatedInput.Buttons.Set(EInputButton.Jump, keyboard.spaceKey.isPressed);
            _accumulatedInput.Buttons.Set(EInputButton.Reload, keyboard.rKey.isPressed);
            _accumulatedInput.Buttons.Set(EInputButton.Pistol, keyboard.digit1Key.isPressed || keyboard.numpad1Key.isPressed);
            _accumulatedInput.Buttons.Set(EInputButton.Rifle, keyboard.digit2Key.isPressed || keyboard.numpad2Key.isPressed);
            _accumulatedInput.Buttons.Set(EInputButton.Shotgun, keyboard.digit3Key.isPressed || keyboard.numpad3Key.isPressed);
            _accumulatedInput.Buttons.Set(EInputButton.Spray, keyboard.fKey.isPressed);

            // Add sprint input
            _accumulatedInput.Buttons.Set(EInputButton.Sprint, keyboard.leftShiftKey.isPressed);
            _accumulatedInput.MoveDirection = moveDirection.normalized;

            // Self mute toggle with M key
            if (keyboard.mKey.wasPressedThisFrame) {
                if (MuteManager.Instance != null) {
                    MuteManager.Instance.ToggleSelfMute();
                }
            }
        }
    }

    private void OnInput(NetworkRunner runner, NetworkInput networkInput) {
        // Mouse movement (delta values) is aligned to engine update.
        // To get perfectly smooth interpolated look, we need to align the mouse input with Fusion ticks.
        _accumulatedInput.LookRotationDelta = _lookRotationAccumulator.ConsumeTickAligned(runner);

        // Fusion polls accumulated input. This callback can be executed multiple times in a row if there is a performance spike.
        networkInput.Set(_accumulatedInput);
    }
}
